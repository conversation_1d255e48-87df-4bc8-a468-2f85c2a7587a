<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\QuestionMaterial;
use App\Models\Question;
use Illuminate\Http\Request;

class QuestionMaterialApiController extends Controller
{
    /**
     * Get all question materials with question counts
     */
    public function index()
    {
        try {
            $user = request()->user();

            // Ad<PERSON> can see all question materials, teachers can only see their own
            if ($user->role === 'admin') {
                $questionMaterials = QuestionMaterial::with('teacher')->get();
            } else {
                $questionMaterials = QuestionMaterial::where('teacher_id', $user->id)->get();
            }

            // Add question counts for each material
            $questionMaterials = $questionMaterials->map(function ($material) {
                $questions = Question::where('questionmaterialid', $material->id)->get();

                // Ensure counts are returned as integers
                $material->pg_count = (int) $questions->where('type', 'pilihan_ganda')->count();
                $material->uraian_count = (int) $questions->where('type', 'uraian_singkat')->count();
                $material->esai_count = (int) $questions->where('type', 'esai')->count();
                $material->total_questions = (int) $questions->count();

                return $material;
            });

            return response()->json([
                'success' => true,
                'message' => 'Question materials retrieved successfully',
                'data' => $questionMaterials
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve question materials',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific question material with details
     */
    public function show($id)
    {
        try {
            $questionMaterial = QuestionMaterial::with('teacher')->find($id);

            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            $questions = Question::with('answers')
                ->where('questionmaterialid', $id)
                ->get();

            // Ensure counts are returned as integers
            $questionMaterial->pg_count = (int) $questions->where('type', 'pilihan_ganda')->count();
            $questionMaterial->uraian_count = (int) $questions->where('type', 'uraian_singkat')->count();
            $questionMaterial->esai_count = (int) $questions->where('type', 'esai')->count();
            $questionMaterial->total_questions = (int) $questions->count();
            $questionMaterial->questions = $questions;

            return response()->json([
                'success' => true,
                'message' => 'Question material retrieved successfully',
                'data' => $questionMaterial
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new question material
     */
    public function store(Request $request)
    {
        try {
            $user = $request->user();

            $validationRules = [
                'name' => 'required|string|max:255',
                'description' => 'required|string',
            ];

            // If admin, require teacher selection
            if ($user->role === 'admin') {
                $validationRules['teacher_id'] = 'required|exists:users,id';
            }

            $request->validate($validationRules);

            $questionMaterial = QuestionMaterial::create([
                'name' => $request->name,
                'description' => $request->description,
                'teacher_id' => $user->role === 'admin' ? $request->teacher_id : $user->id,
            ]);

            // Reload with teacher relationship
            $questionMaterial = QuestionMaterial::with('teacher')->find($questionMaterial->id);

            // Add default counts
            $questionMaterial->pg_count = 0;
            $questionMaterial->uraian_count = 0;
            $questionMaterial->esai_count = 0;
            $questionMaterial->total_questions = 0;

            return response()->json([
                'success' => true,
                'message' => 'Question material created successfully',
                'data' => $questionMaterial
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update question material
     */
    public function update(Request $request, $id)
    {
        try {
            $questionMaterial = QuestionMaterial::find($id);

            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            $user = $request->user();

            $validationRules = [
                'name' => 'required|string|max:255',
                'description' => 'required|string',
            ];

            // If admin, require teacher selection
            if ($user->role === 'admin') {
                $validationRules['teacher_id'] = 'required|exists:users,id';
            }

            $request->validate($validationRules);

            $updateData = [
                'name' => $request->name,
                'description' => $request->description,
            ];

            // Only update teacher_id if admin
            if ($user->role === 'admin') {
                $updateData['teacher_id'] = $request->teacher_id;
            }

            $questionMaterial->update($updateData);

            // Reload with teacher relationship
            $questionMaterial = QuestionMaterial::with('teacher')->find($id);

            // Add question counts
            $questions = Question::where('questionmaterialid', $id)->get();
            $questionMaterial->pg_count = $questions->where('type', 'pilihan_ganda')->count();
            $questionMaterial->uraian_count = $questions->where('type', 'uraian_singkat')->count();
            $questionMaterial->esai_count = $questions->where('type', 'esai')->count();
            $questionMaterial->total_questions = $questions->count();

            return response()->json([
                'success' => true,
                'message' => 'Question material updated successfully',
                'data' => $questionMaterial
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update question material scores
     */
    public function updateScores(Request $request, $id)
    {
        try {
            $questionMaterial = QuestionMaterial::find($id);

            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            $request->validate([
                'pg_total_score' => 'nullable|integer|min:0',
                'uraian_total_score' => 'nullable|integer|min:0',
            ]);

            $questionMaterial->update([
                'pg_total_score' => $request->input('pg_total_score'),
                'uraian_total_score' => $request->input('uraian_total_score'),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Scores updated successfully',
                'data' => $questionMaterial
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update scores',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete question material
     */
    public function destroy($id)
    {
        try {
            $questionMaterial = QuestionMaterial::find($id);

            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            // Delete related questions first
            Question::where('questionmaterialid', $id)->delete();

            // Delete the question material
            $questionMaterial->delete();

            return response()->json([
                'success' => true,
                'message' => 'Question material deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
