<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class TeacherApiController extends Controller
{
    /**
     * Get all teachers
     */
    public function index()
    {
        try {
            $teachers = User::where('role', 'teacher')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Teachers retrieved successfully',
                'data' => [
                    'teachers' => UserResource::collection($teachers)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve teachers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get teacher by ID
     */
    public function show($id)
    {
        try {
            $teacher = User::where('role', 'teacher')->find($id);

            if (!$teacher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Teacher not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Teacher retrieved successfully',
                'data' => [
                    'teacher' => new UserResource($teacher)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve teacher',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new teacher
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'nip' => 'required|string|max:255|unique:users,nip',
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|string|min:6',
                'gender' => 'required|in:male,female',
                'phonenumber' => 'required|string|max:20',
                'address' => 'nullable|string'
            ]);

            // Format phone number
            $phonenumber = $request->phonenumber;
            if (substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            $teacher = User::create([
                'nip' => $request->nip,
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'gender' => $request->gender,
                'role' => 'teacher',
                'phonenumber' => $phonenumber,
                'address' => $request->address,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Teacher created successfully',
                'data' => [
                    'teacher' => new UserResource($teacher)
                ]
            ], 201);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create teacher',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update teacher
     */
    public function update(Request $request, $id)
    {
        try {
            $teacher = User::where('role', 'teacher')->find($id);

            if (!$teacher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Teacher not found'
                ], 404);
            }

            $request->validate([
                'nip' => [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique('users', 'nip')->ignore($id),
                ],
                'name' => 'required|string|max:255',
                'email' => [
                    'required',
                    'email',
                    Rule::unique('users')->ignore($id),
                ],
                'password' => 'nullable|string|min:6',
                'gender' => 'required|in:male,female',
                'phonenumber' => 'required|string|max:20',
                'address' => 'nullable|string'
            ]);

            // Format phone number
            $phonenumber = $request->phonenumber;
            if (substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            $teacher->nip = $request->nip;
            $teacher->name = $request->name;
            $teacher->email = $request->email;
            $teacher->gender = $request->gender;
            $teacher->phonenumber = $phonenumber;
            $teacher->address = $request->address;

            // Only update password if provided
            if ($request->filled('password')) {
                $teacher->password = Hash::make($request->password);
            }

            $teacher->save();

            return response()->json([
                'success' => true,
                'message' => 'Teacher updated successfully',
                'data' => [
                    'teacher' => new UserResource($teacher)
                ]
            ], 200);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update teacher',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete teacher
     */
    public function destroy($id)
    {
        try {
            $teacher = User::where('role', 'teacher')->find($id);

            if (!$teacher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Teacher not found'
                ], 404);
            }

            $teacher->delete();

            return response()->json([
                'success' => true,
                'message' => 'Teacher deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete teacher',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
