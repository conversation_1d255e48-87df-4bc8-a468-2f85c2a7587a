<!-- <PERSON><PERSON>er -->
<div class="flex items-center justify-between p-6 border-b border-gray-100">
    <div class="flex items-center space-x-4">
        <!-- Icon -->
        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                </path>
            </svg>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-800">Edit Si<PERSON>wa</h3>
            <p class="text-sm text-gray-500">Ubah informasi siswa {{ $student->name }}</p>
        </div>
    </div>
    <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
    </button>
</div>

<!-- Modal Body -->
<div class="p-6 max-h-[70vh] overflow-y-auto">
    <form id="editStudentForm" action="{{ route('student.edit.post', $student->id) }}" method="POST"
        autocomplete="off">
        @csrf
        @method('PUT')
        <div class="space-y-4">
            <!-- NISN & NIS -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="nisn" class="block text-sm font-medium text-gray-700 mb-2">
                        NISN <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="nisn" name="nisn" value="{{ $student->nisn }}" placeholder="Masukkan NISN" required>
                    @error('nisn')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <div>
                    <label for="nis" class="block text-sm font-medium text-gray-700 mb-2">
                        NIS <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="nis" name="nis" value="{{ $student->nis }}" placeholder="Masukkan NIS" required>
                    @error('nis')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <!-- Nama & Email -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Nama <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="name" name="name" value="{{ $student->name }}" placeholder="Masukkan nama lengkap"
                        required>
                    @error('name')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <input type="email"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="email" name="email" value="{{ $student->user->email ?? '' }}"
                        placeholder="<EMAIL>" required>
                    @error('email')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <!-- Password -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    Password <span class="text-gray-500 text-xs">(Kosongkan jika tidak ingin mengubah)</span>
                </label>
                <input type="password"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="password" name="password" placeholder="Masukkan password baru">
                @error('password')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Kelas & Nama Orang Tua -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="classid" class="block text-sm font-medium text-gray-700 mb-2">
                        Kelas <span class="text-red-500">*</span>
                    </label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="classid" name="classid" required>
                        <option value="">Pilih kelas</option>
                        @foreach ($classStudent as $class)
                            <option value="{{ $class->id }}"
                                {{ $student->classid == $class->id ? 'selected' : '' }}>
                                {{ $class->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('classid')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <div>
                    <label for="parentname" class="block text-sm font-medium text-gray-700 mb-2">
                        Nama Orang Tua <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="parentname" name="parentname" value="{{ $student->parentname }}"
                        placeholder="Masukkan nama orang tua" required>
                    @error('parentname')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <!-- Gender & Phone -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                        Jenis Kelamin <span class="text-red-500">*</span>
                    </label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="gender" name="gender" required>
                        <option value="">Pilih jenis kelamin</option>
                        <option value="male" {{ $student->gender == 'male' ? 'selected' : '' }}>Laki-laki</option>
                        <option value="female" {{ $student->gender == 'female' ? 'selected' : '' }}>Perempuan</option>
                    </select>
                    @error('gender')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <div>
                    <label for="phonenumber" class="block text-sm font-medium text-gray-700 mb-2">
                        Nomor WhatsApp <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        id="phonenumber" name="phonenumber" value="{{ $student->phonenumber }}"
                        placeholder="08xxxxxxxxxx" required>
                    @error('phonenumber')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <!-- Religion -->
            <div>
                <label for="religion" class="block text-sm font-medium text-gray-700 mb-2">
                    Agama<span class="text-red-500 ml-1">*</span>
                </label>
                @php
                    $religion = ['Islam', 'Kristen', 'Katolik', 'Hindu', 'Budha', 'Konghucu'];
                @endphp
                <select
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="religion" name="religion" required>
                    <option value="">Pilih agama</option>
                    @foreach ($religion as $value)
                        <option value="{{ $value }}" {{ $student->religion == $value ? 'selected' : '' }}>
                            {{ $value }}
                        </option>
                    @endforeach
                </select>
                @error('religion')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Address -->
            <div>
                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                    Alamat
                </label>
                <textarea
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    id="address" name="address" rows="3" placeholder="Masukkan alamat lengkap">{{ $student->address }}</textarea>
                @error('address')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>
    </form>
</div>

<!-- Modal Footer -->
<div class="flex items-center justify-end space-x-3 p-4 border-t border-gray-100 bg-gray-50 rounded-b-2xl">
    <button type="button" onclick="closeModal()"
        class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
        Batal
    </button>
    <button type="submit" form="editStudentForm"
        class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center space-x-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>Update Siswa</span>
    </button>
</div>
