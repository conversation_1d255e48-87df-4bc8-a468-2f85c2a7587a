import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/student_provider.dart';
import '../../models/student.dart';

class StudentFormPage extends StatefulWidget {
  final Student? student;

  const StudentFormPage({super.key, this.student});

  @override
  State<StudentFormPage> createState() => _StudentFormPageState();
}

class _StudentFormPageState extends State<StudentFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nisnController = TextEditingController();
  final _nisController = TextEditingController();
  final _nameController = TextEditingController();
  final _parentNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  String _selectedGender = 'male';
  String? _selectedReligion;
  int? _selectedClassId;
  bool _obscurePassword = true;
  bool _isLoading = false;

  // Religion options
  final List<String> _religionOptions = [
    'Islam',
    'Kristen',
    'Katolik',
    'Hindu',
    'Budha',
    'Konghucu',
  ];

  bool get isEditing => widget.student != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _populateFields();
    }
    // Load classes when form opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StudentProvider>().loadClasses();
    });
  }

  void _populateFields() {
    final student = widget.student!;
    _nisnController.text = student.nisn ?? '';
    _nisController.text = student.nis ?? '';
    _nameController.text = student.name;
    _parentNameController.text = student.parentName ?? '';
    _phoneController.text = student.formattedPhoneNumber;
    _selectedReligion = student.religion;
    _addressController.text = student.address ?? '';
    _emailController.text = student.email;
    _selectedGender = student.gender ?? 'male';
    // Only set _selectedClassId if it's valid and exists in the classes list
    // We'll validate this after classes are loaded
    _selectedClassId = student.classId;
  }

  @override
  void dispose() {
    _nisnController.dispose();
    _nisController.dispose();
    _nameController.dispose();
    _parentNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName tidak boleh kosong';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email tidak boleh kosong';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Format email tidak valid';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    // Password is optional for both create and edit - allow empty for default
    if (value != null && value.isNotEmpty && value.length < 6) {
      return 'Password minimal 6 karakter';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Nomor telepon tidak boleh kosong';
    }
    String cleanPhone = value.replaceAll(' ', '').replaceAll('-', '');
    if (!RegExp(r'^[0-9+]+$').hasMatch(cleanPhone)) {
      return 'Nomor telepon hanya boleh berisi angka';
    }
    // Remove country code for length validation
    String phoneForLength =
        cleanPhone.startsWith('+62')
            ? cleanPhone.substring(3)
            : cleanPhone.startsWith('62')
            ? cleanPhone.substring(2)
            : cleanPhone.startsWith('0')
            ? cleanPhone.substring(1)
            : cleanPhone;
    if (phoneForLength.length < 9) {
      return 'Nomor telepon minimal 10 digit';
    }
    return null;
  }

  String? _validateNISN(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'NISN tidak boleh kosong';
    }
    if (!RegExp(r'^[0-9]+$').hasMatch(value.replaceAll(' ', ''))) {
      return 'NISN hanya boleh berisi angka';
    }
    return null;
  }

  String? _validateNIS(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'NIS tidak boleh kosong';
    }
    if (!RegExp(r'^[0-9]+$').hasMatch(value.replaceAll(' ', ''))) {
      return 'NIS hanya boleh berisi angka';
    }
    return null;
  }

  String? _validateReligion(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Agama tidak boleh kosong';
    }
    return null;
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedClassId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Silakan pilih kelas'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_selectedReligion == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Silakan pilih agama'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final studentProvider = context.read<StudentProvider>();
      bool success;

      if (isEditing) {
        success = await studentProvider.updateStudent(
          id: widget.student!.id,
          nisn: _nisnController.text.trim(),
          nis: _nisController.text.trim(),
          name: _nameController.text.trim(),
          parentname: _parentNameController.text.trim(),
          gender: _selectedGender,
          phonenumber: _phoneController.text.trim(),
          religion: _selectedReligion,
          address:
              _addressController.text.trim().isEmpty
                  ? null
                  : _addressController.text.trim(),
          classid: _selectedClassId!,
          email: _emailController.text.trim(),
          password:
              _passwordController.text.trim().isEmpty
                  ? null
                  : _passwordController.text.trim(),
        );
      } else {
        success = await studentProvider.createStudent(
          nisn: _nisnController.text.trim(),
          nis: _nisController.text.trim(),
          name: _nameController.text.trim(),
          parentname: _parentNameController.text.trim(),
          gender: _selectedGender,
          phonenumber: _phoneController.text.trim(),
          religion: _selectedReligion,
          address:
              _addressController.text.trim().isEmpty
                  ? null
                  : _addressController.text.trim(),
          classid: _selectedClassId!,
          email: _emailController.text.trim(),
          password:
              _passwordController.text.trim().isEmpty
                  ? null
                  : _passwordController.text.trim(),
        );
      }

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isEditing
                    ? 'Siswa berhasil diperbarui'
                    : 'Siswa berhasil ditambahkan',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                studentProvider.errorMessage ?? 'Terjadi kesalahan',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FD),
      appBar: AppBar(
        title: Text(
          isEditing ? 'Edit Siswa' : 'Tambah Siswa',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF455A9D),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildFormCard(),
              const SizedBox(height: 20),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  controller: _nisnController,
                  label: 'NISN',
                  hint: 'Masukkan NISN',
                  icon: Icons.badge_outlined,
                  keyboardType: TextInputType.number,
                  validator: _validateNISN,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTextField(
                  controller: _nisController,
                  label: 'NIS',
                  hint: 'Masukkan NIS',
                  icon: Icons.badge_outlined,
                  keyboardType: TextInputType.number,
                  validator: _validateNIS,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _nameController,
            label: 'Nama Lengkap',
            hint: 'Masukkan nama lengkap siswa',
            icon: Icons.person_outline,
            validator: (value) => _validateRequired(value, 'Nama'),
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _parentNameController,
            label: 'Nama Orang Tua',
            hint: 'Masukkan nama orang tua',
            icon: Icons.family_restroom_outlined,
            validator: (value) => _validateRequired(value, 'Nama orang tua'),
          ),
          const SizedBox(height: 16),
          _buildGenderField(),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _phoneController,
            label: 'Nomor Telepon',
            hint: 'Masukkan nomor telepon',
            icon: Icons.phone_outlined,
            keyboardType: TextInputType.phone,
            validator: _validatePhone,
          ),
          const SizedBox(height: 16),
          _buildReligionDropdown(),
          const SizedBox(height: 16),
          _buildClassDropdown(),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _emailController,
            label: 'Email',
            hint: 'Masukkan email siswa',
            icon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            validator: _validateEmail,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _passwordController,
            label:
                isEditing
                    ? 'Password (kosongkan jika tidak diubah)'
                    : 'Password (kosongkan untuk default)',
            hint: 'Masukkan password',
            icon: Icons.lock_outline,
            obscureText: _obscurePassword,
            validator: _validatePassword,
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off : Icons.visibility,
                color: const Color(0xFF999999),
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _addressController,
            label: 'Alamat (Opsional)',
            hint: 'Masukkan alamat siswa',
            icon: Icons.location_on_outlined,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    int maxLines = 1,
    String? Function(String?)? validator,
    Widget? suffixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: const Color(0xFF455A9D)),
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF455A9D)),
            ),
            filled: true,
            fillColor: const Color(0xFFF5F5F5),
          ),
        ),
      ],
    );
  }

  Widget _buildGenderField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Jenis Kelamin',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedGender,
              isExpanded: true,
              icon: const Icon(Icons.arrow_drop_down, color: Color(0xFF455A9D)),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedGender = newValue!;
                });
              },
              items: const [
                DropdownMenuItem(value: 'male', child: Text('Laki-laki')),
                DropdownMenuItem(value: 'female', child: Text('Perempuan')),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildClassDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Kelas',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),
        Consumer<StudentProvider>(
          builder: (context, studentProvider, child) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE0E0E0)),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int?>(
                  value:
                      studentProvider.classes.any(
                            (cls) => cls.id == _selectedClassId,
                          )
                          ? _selectedClassId
                          : null,
                  isExpanded: true,
                  hint: const Row(
                    children: [
                      Icon(Icons.class_, color: Color(0xFF455A9D)),
                      SizedBox(width: 12),
                      Text('Pilih kelas'),
                    ],
                  ),
                  icon: const Icon(
                    Icons.arrow_drop_down,
                    color: Color(0xFF455A9D),
                  ),
                  onChanged: (int? newValue) {
                    setState(() {
                      _selectedClassId = newValue;
                    });
                  },
                  items:
                      studentProvider.classes.map((cls) {
                        return DropdownMenuItem<int?>(
                          value: cls.id,
                          child: Text(
                            '${cls.name} ${cls.level != null ? '(${cls.level})' : ''}',
                          ),
                        );
                      }).toList(),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildReligionDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Agama',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String?>(
              value: _selectedReligion,
              isExpanded: true,
              hint: const Row(
                children: [
                  Icon(Icons.mosque_outlined, color: Color(0xFF455A9D)),
                  SizedBox(width: 12),
                  Text('Pilih agama'),
                ],
              ),
              icon: const Icon(Icons.arrow_drop_down, color: Color(0xFF455A9D)),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedReligion = newValue;
                });
              },
              items:
                  _religionOptions.map((religion) {
                    return DropdownMenuItem<String?>(
                      value: religion,
                      child: Text(religion),
                    );
                  }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF455A9D),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 2,
        ),
        child:
            _isLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : Text(
                  isEditing ? 'Perbarui Siswa' : 'Tambah Siswa',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
      ),
    );
  }
}
