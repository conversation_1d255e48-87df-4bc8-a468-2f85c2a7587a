<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StudentClassResource;
use App\Models\StudentClass;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class StudentClassApiController extends Controller
{
    /**
     * Get all classes
     */
    public function index()
    {
        try {
            $classes = StudentClass::with('teacher')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Classes retrieved successfully',
                'data' => [
                    'classes' => StudentClassResource::collection($classes)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve classes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get class by ID
     */
    public function show($id)
    {
        try {
            $class = StudentClass::with('teacher')
                ->find($id);

            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Class not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Class retrieved successfully',
                'data' => [
                    'class' => new StudentClassResource($class)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve class',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new class
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:class,name',
                'teacherid' => 'nullable|exists:users,id',
                'level' => 'nullable|string|max:255',
            ]);

            // Validate teacher role if teacherid is provided
            if ($request->teacherid) {
                $teacher = User::find($request->teacherid);
                if (!$teacher || $teacher->role !== 'teacher') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Selected user is not a teacher'
                    ], 422);
                }
            }

            $class = StudentClass::create([
                'name' => $request->name,
                'teacherid' => $request->teacherid,
                'level' => $request->level,
            ]);

            // Load teacher relationship
            $class->load('teacher');

            return response()->json([
                'success' => true,
                'message' => 'Class created successfully',
                'data' => [
                    'class' => new StudentClassResource($class)
                ]
            ], 201);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create class',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update class
     */
    public function update(Request $request, $id)
    {
        try {
            $class = StudentClass::find($id);

            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Class not found'
                ], 404);
            }

            $request->validate([
                'name' => 'required|string|max:255|unique:class,name,' . $id,
                'teacherid' => 'nullable|exists:users,id',
                'level' => 'nullable|string|max:255',
            ]);

            // Validate teacher role if teacherid is provided
            if ($request->teacherid) {
                $teacher = User::find($request->teacherid);
                if (!$teacher || $teacher->role !== 'teacher') {
                    return response()->json([
                        'success' => false,
                        'message' => 'Selected user is not a teacher'
                    ], 422);
                }
            }

            $class->update([
                'name' => $request->name,
                'teacherid' => $request->teacherid,
                'level' => $request->level,
            ]);

            // Load teacher relationship
            $class->load('teacher');

            return response()->json([
                'success' => true,
                'message' => 'Class updated successfully',
                'data' => [
                    'class' => new StudentClassResource($class)
                ]
            ], 200);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update class',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete class (hard delete)
     */
    public function destroy($id)
    {
        try {
            $class = StudentClass::find($id);

            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Class not found'
                ], 404);
            }

            // Check if class has students
            if ($class->students()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete class that has students. Please move students to another class first.'
                ], 422);
            }

            // Hard delete
            $class->delete();

            return response()->json([
                'success' => true,
                'message' => 'Class deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete class',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all teachers for dropdown
     */
    public function getTeachers()
    {
        try {
            $teachers = User::where('role', 'teacher')
                ->select('id', 'name', 'email', 'nip')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Teachers retrieved successfully',
                'data' => [
                    'teachers' => $teachers
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve teachers',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
