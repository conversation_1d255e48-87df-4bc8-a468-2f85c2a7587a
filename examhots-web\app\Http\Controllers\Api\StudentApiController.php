<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StudentResource;
use App\Models\Student;
use App\Models\User;
use App\Models\StudentClass;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class StudentApiController extends Controller
{
    /**
     * Get all students
     */
    public function index()
    {
        try {
            $students = Student::with(['class', 'user'])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Students retrieved successfully',
                'data' => [
                    'students' => StudentResource::collection($students)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve students',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student by ID
     */
    public function show($id)
    {
        try {
            $student = Student::with(['class', 'user'])->find($id);

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Student retrieved successfully',
                'data' => [
                    'student' => new StudentResource($student)
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new student
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'nisn' => 'required|numeric|unique:student,nisn',
                'nis' => 'required|numeric',
                'name' => 'required|string|max:255',
                'parentname' => 'required|string|max:255',
                'gender' => 'required|in:male,female',
                'phonenumber' => 'required|numeric',
                'religion' => 'required|string|max:255',
                'address' => 'nullable|string',
                'classid' => 'required|exists:class,id',
                'email' => 'required|email|unique:users,email',
                'password' => 'nullable|string|min:6',
            ]);

            // Validate class exists
            $class = StudentClass::where('id', $request->classid)
                ->first();

            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected class not found or has been deleted'
                ], 422);
            }

            // Format phone number
            $phonenumber = $request->phonenumber;
            if (substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            // Create student
            $student = Student::create([
                'nisn' => $request->nisn,
                'nis' => $request->nis,
                'classid' => $request->classid,
                'name' => $request->name,
                'parentname' => $request->parentname,
                'phonenumber' => $phonenumber,
                'gender' => $request->gender,
                'religion' => $request->religion,
                'address' => $request->address,
            ]);

            // Create user account
            User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->filled('password') ? $request->password : '123456'),
                'role' => 'student',
                'studentid' => $student->id,
            ]);

            // Load relationships
            $student->load(['class', 'user']);

            return response()->json([
                'success' => true,
                'message' => 'Student created successfully',
                'data' => [
                    'student' => new StudentResource($student)
                ]
            ], 201);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update student
     */
    public function update(Request $request, $id)
    {
        try {
            $student = Student::with('user')->find($id);

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student not found'
                ], 404);
            }

            $request->validate([
                'nisn' => [
                    'required',
                    'numeric',
                    Rule::unique('student')->ignore($id),
                ],
                'nis' => 'required|numeric',
                'name' => 'required|string|max:255',
                'parentname' => 'required|string|max:255',
                'gender' => 'required|in:male,female',
                'phonenumber' => 'required|numeric',
                'religion' => 'required|string|max:255',
                'address' => 'nullable|string',
                'classid' => 'required|exists:class,id',
                'email' => [
                    'required',
                    'email',
                    Rule::unique('users')->ignore($student->user?->id),
                ],
                'password' => 'nullable|string|min:6',
            ]);

            // Validate class exists
            $class = StudentClass::where('id', $request->classid)
                ->first();

            if (!$class) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected class not found or has been deleted'
                ], 422);
            }

            // Format phone number
            $phonenumber = $request->phonenumber;
            if (substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            // Update student
            $student->update([
                'nisn' => $request->nisn,
                'nis' => $request->nis,
                'classid' => $request->classid,
                'name' => $request->name,
                'parentname' => $request->parentname,
                'phonenumber' => $phonenumber,
                'gender' => $request->gender,
                'religion' => $request->religion,
                'address' => $request->address,
            ]);

            // Update user account if exists
            if ($student->user) {
                $student->user->update([
                    'name' => $request->name,
                    'email' => $request->email,
                    'password' => $request->filled('password')
                        ? Hash::make($request->password)
                        : $student->user->password,
                ]);
            }

            // Load relationships
            $student->load(['class', 'user']);

            return response()->json([
                'success' => true,
                'message' => 'Student updated successfully',
                'data' => [
                    'student' => new StudentResource($student)
                ]
            ], 200);
        } catch (ValidationException $e) {
            // Create specific error message based on validation errors
            $errorMessages = [];
            foreach ($e->errors() as $messages) {
                $errorMessages = array_merge($errorMessages, $messages);
            }

            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', $errorMessages),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete student
     */
    public function destroy($id)
    {
        try {
            $student = Student::with('user')->find($id);

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student not found'
                ], 404);
            }

            // Delete user account if exists
            if ($student->user) {
                $student->user->delete();
            }

            // Delete student
            $student->delete();

            return response()->json([
                'success' => true,
                'message' => 'Student deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all classes for dropdown
     */
    public function getClasses()
    {
        try {
            $classes = StudentClass::select('id', 'name', 'level')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Classes retrieved successfully',
                'data' => [
                    'classes' => $classes
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve classes',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
