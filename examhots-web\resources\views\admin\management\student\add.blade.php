<!-- <PERSON><PERSON>er -->
<div class="flex items-center justify-between p-6 border-b border-gray-100">
    <div class="flex items-center space-x-4">
        <!-- Icon -->
        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                </path>
            </svg>
        </div>
        <!-- Title and Subtitle -->
        <div>
            <h2 class="text-xl font-semibold text-gray-900">Tambah Siswa Baru</h2>
            <p class="text-sm text-gray-500 mt-1">Isi informasi siswa untuk menambahkan ke sistem</p>
        </div>
    </div>
    <!-- Close Button -->
    <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors" onclick="closeModal()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>
<form action="{{ route('student.add.post') }}" method="POST" autocomplete="off">
    @csrf
    <!-- Modal Body -->
    <div class="p-6 space-y-4 max-h-[70vh] overflow-y-auto">
        <!-- NISN & NIS -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="nisn" class="block text-sm font-medium text-gray-700 mb-2">
                    NISN<span class="text-red-500 ml-1">*</span>
                </label>
                <input type="number" id="nisn" name="nisn" placeholder="Masukkan NISN"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                @error('nisn')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label for="nis" class="block text-sm font-medium text-gray-700 mb-2">
                    NIS<span class="text-red-500 ml-1">*</span>
                </label>
                <input type="number" id="nis" name="nis" placeholder="Masukkan NIS"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                @error('nis')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>

        <!-- Nama & Email -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Nama<span class="text-red-500 ml-1">*</span>
                </label>
                <input type="text" id="name" name="name" placeholder="Masukkan nama lengkap"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                @error('name')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email<span class="text-red-500 ml-1">*</span>
                </label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                @error('email')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>

        <!-- Password -->
        <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                Password
            </label>
            <input type="password" id="password" name="password" placeholder="Masukkan password (opsional)"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200">
            @error('password')
                <span class="text-red-500 text-sm">{{ $message }}</span>
            @enderror
        </div>

        <!-- Kelas & Nama Orang Tua -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="classid" class="block text-sm font-medium text-gray-700 mb-2">
                    Kelas<span class="text-red-500 ml-1">*</span>
                </label>
                <select id="classid" name="classid"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                    <option value="">Pilih kelas</option>
                    @foreach ($classStudent as $class)
                        <option value="{{ $class->id }}">{{ $class->name }}</option>
                    @endforeach
                </select>
                @error('classid')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label for="parentname" class="block text-sm font-medium text-gray-700 mb-2">
                    Nama Orang Tua<span class="text-red-500 ml-1">*</span>
                </label>
                <input type="text" id="parentname" name="parentname" placeholder="Masukkan nama orang tua"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                @error('parentname')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>

        <!-- Gender & Phone -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                    Jenis Kelamin<span class="text-red-500 ml-1">*</span>
                </label>
                <select id="gender" name="gender"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                    <option value="">Pilih jenis kelamin</option>
                    <option value="male">Laki-laki</option>
                    <option value="female">Perempuan</option>
                </select>
                @error('gender')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label for="phonenumber" class="block text-sm font-medium text-gray-700 mb-2">
                    Nomor WhatsApp<span class="text-red-500 ml-1">*</span>
                </label>
                <input type="number" id="phonenumber" name="phonenumber" placeholder="08xxxxxxxxxx"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                    required>
                @error('phonenumber')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>
        </div>

        <!-- Religion -->
        <div>
            <label for="religion" class="block text-sm font-medium text-gray-700 mb-2">
                Agama<span class="text-red-500 ml-1">*</span>
            </label>
            @php
                $religion = ['Islam', 'Kristen', 'Katolik', 'Hindu', 'Budha', 'Konghucu'];
            @endphp
            <select id="religion" name="religion"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200"
                required>
                <option value="">Pilih agama</option>
                @foreach ($religion as $value)
                    <option value="{{ $value }}">{{ $value }}</option>
                @endforeach
            </select>
            @error('religion')
                <span class="text-red-500 text-sm">{{ $message }}</span>
            @enderror
        </div>

        <!-- Address -->
        <div>
            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                Alamat
            </label>
            <textarea id="address" name="address" rows="3" placeholder="Masukkan alamat lengkap"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200 resize-none"></textarea>
            @error('address')
                <span class="text-red-500 text-sm">{{ $message }}</span>
            @enderror
        </div>
    </div>

    <!-- Modal Footer -->
    <div class="flex items-center justify-end space-x-3 p-4 border-t border-gray-100">
        <button type="button" onclick="closeModal()"
            class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button type="submit"
            class="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
            Simpan Siswa
        </button>
    </div>
</form>
